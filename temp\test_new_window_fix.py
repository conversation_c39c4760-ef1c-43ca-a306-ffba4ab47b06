#!/usr/bin/env python3
"""
测试新窗口模型显示修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from uedf_demo import VTKURDFViewer, TestVTKWindow
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import QTimer

class TestNewWindowFix(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试新窗口模型显示修复")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 说明标签
        info_label = QLabel("""
测试步骤：
1. 点击"创建主窗口"按钮
2. 在主窗口中点击"加载URDF"加载模型
3. 点击"新窗口"按钮创建新窗口
4. 观察新窗口是否立即显示模型（无需手动滑动窗口）

修复内容：
- 添加了VTK上下文管理
- 增强了窗口初始化流程
- 添加了多次延迟渲染
- 确保窗口完全显示后再加载模型
        """)
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 创建主窗口按钮
        create_btn = QPushButton("创建主窗口")
        create_btn.clicked.connect(self.create_main_window)
        layout.addWidget(create_btn)
        
        self.main_window = None
    
    def create_main_window(self):
        """创建主窗口"""
        try:
            # if self.main_window:
            #     self.main_window.close()
            
            self.main_window = TestVTKWindow()
            self.main_window.show()
            
            print("主窗口已创建，请在主窗口中测试新窗口功能")
            
        except Exception as e:
            print(f"创建主窗口失败: {e}")
            import traceback
            traceback.print_exc()
    
    def closeEvent(self, event):
        """关闭时清理资源"""
        if self.main_window:
            try:
                self.main_window.close()
            except:
                pass
        event.accept()

def main():
    app = QApplication(sys.argv)
    
    window = TestNewWindowFix()
    window.show()
    
    print("新窗口显示修复测试启动")
    print("请按照窗口中的说明进行测试")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
